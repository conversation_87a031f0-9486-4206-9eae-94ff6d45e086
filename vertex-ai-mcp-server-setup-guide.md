# Vertex AI MCP Server Setup Guide

## Overview
This guide will help you set up the Vertex AI MCP Server to optimize your Augment Agent workflow by offloading heavy research tasks and large file operations to Google's Vertex AI.

## Prerequisites
- ✅ Vertex AI MCP Server installed and built
- Google Cloud account with billing enabled
- Node.js and Bun installed

## Step 1: Google Cloud Setup

### 1.1 Create or Select a Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note your PROJECT_ID (you'll need this later)

### 1.2 Enable Required APIs
Run these commands in Google Cloud Shell or with gcloud CLI:

```bash
# Enable Vertex AI API
gcloud services enable aiplatform.googleapis.com

# Enable Cloud Resource Manager API (if needed)
gcloud services enable cloudresourcemanager.googleapis.com
```

### 1.3 Create Service Account
```bash
# Create service account
gcloud iam service-accounts create vertex-ai-mcp-server \
    --description="Service account for Vertex AI MCP Server" \
    --display-name="Vertex AI MCP Server"

# Grant necessary permissions
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:vertex-ai-mcp-server@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"

# Create and download service account key
gcloud iam service-accounts keys create ~/vertex-ai-mcp-server-key.json \
    --iam-account=vertex-ai-mcp-server@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

## Step 2: Environment Configuration

### 2.1 Required Environment Variables
Create a `.env` file in your vertex-ai-mcp-server directory:

```bash
# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/vertex-ai-mcp-server-key.json

# Vertex AI Model Configuration
VERTEX_AI_MODEL_ID=gemini-2.5-pro-exp-03-25
VERTEX_AI_TEMPERATURE=0.1
VERTEX_AI_USE_STREAMING=true
```

### 2.2 Alternative: Using gcloud CLI Authentication
Instead of service account key, you can use:
```bash
gcloud auth application-default login
```

## Step 3: Augment Configuration

### 3.1 MCP Server Configuration
Add this to your Augment settings:

**Command Node:** `/Users/<USER>/Desktop/brid1/vertex-ai-mcp-server/build/index.js`

**Environment Variables:**
- `GOOGLE_CLOUD_PROJECT`: your-project-id
- `GOOGLE_CLOUD_LOCATION`: us-central1  
- `GOOGLE_APPLICATION_CREDENTIALS`: /path/to/your/vertex-ai-mcp-server-key.json
- `VERTEX_AI_MODEL_ID`: gemini-2.5-pro-exp-03-25
- `VERTEX_AI_TEMPERATURE`: 0.1
- `VERTEX_AI_USE_STREAMING`: true

## Step 4: Testing the Setup

### 4.1 Test the MCP Server
```bash
cd /Users/<USER>/Desktop/brid1/vertex-ai-mcp-server
node build/index.js
```

### 4.2 Verify Environment Variables
```bash
# Check if credentials file exists
ls -la $GOOGLE_APPLICATION_CREDENTIALS

# Test gcloud authentication
gcloud auth list

# Test Vertex AI access
gcloud ai models list --region=us-central1
```

## Available Models
- `gemini-2.5-pro-exp-03-25` (Recommended - 1M context)
- `gemini-1.5-pro`
- `gemini-1.5-flash`

## Benefits
- **Context Management**: Offloads large operations from Augment's 200k context limit
- **Research Tasks**: Handles extensive research with 1M context window
- **File Operations**: Processes large files without consuming Augment's context
- **Cost Effective**: Uses free Vertex AI API tier

## Troubleshooting
- Ensure billing is enabled on your Google Cloud project
- Verify service account has proper permissions
- Check that all required APIs are enabled
- Confirm environment variables are correctly set

## Next Steps
Once configured, the Vertex AI MCP Server will automatically handle:
- Large file analysis
- Extensive research tasks
- Complex code operations
- Documentation generation

This allows Augment Agent to focus on precise code edits and user interaction.
