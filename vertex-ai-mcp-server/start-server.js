#!/usr/bin/env node

// Wrapper script to ensure environment variables are loaded before starting the MCP server
import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Get the directory of this script
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env file
const envPath = join(__dirname, '.env');
config({ path: envPath });

console.log('🔧 Loading environment variables...');
console.log(`📋 AI Provider: ${process.env.AI_PROVIDER || 'not set'}`);
console.log(`🔑 API Key: ${process.env.GEMINI_API_KEY ? 'loaded' : 'not set'}`);
console.log(`🤖 Model: ${process.env.GEMINI_MODEL_ID || 'not set'}`);

// Validate required environment variables
if (process.env.AI_PROVIDER === 'gemini' && !process.env.GEMINI_API_KEY) {
    console.error('❌ Error: GEMINI_API_KEY is required when AI_PROVIDER is gemini');
    process.exit(1);
}

if (process.env.AI_PROVIDER === 'vertex' && !process.env.GOOGLE_CLOUD_PROJECT) {
    console.error('❌ Error: GOOGLE_CLOUD_PROJECT is required when AI_PROVIDER is vertex');
    process.exit(1);
}

console.log('✅ Environment variables validated');
console.log('🚀 Starting MCP server...\n');

// Import and start the actual MCP server
import('./build/index.js');
