#!/usr/bin/env node

// Direct test of the MCP server functionality
import { config } from 'dotenv';

// Load environment variables
config();

console.log('🔧 Testing MCP Server Direct Connection...\n');

// Test if we can import the server
try {
    console.log('📡 Testing server startup...');
    
    // This will test if the server starts without errors
    const { spawn } = await import('child_process');
    
    const serverProcess = spawn('node', ['build/index.js'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: process.env
    });
    
    let output = '';
    let errorOutput = '';
    
    serverProcess.stdout.on('data', (data) => {
        output += data.toString();
    });
    
    serverProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
    });
    
    // Give it 3 seconds to start
    setTimeout(() => {
        serverProcess.kill();
        
        console.log('📊 Server Test Results:');
        if (errorOutput.includes('Error') || errorOutput.includes('error')) {
            console.log('❌ Server had errors:');
            console.log(errorOutput);
        } else {
            console.log('✅ Server started successfully!');
            if (output) {
                console.log('📝 Server output:', output.substring(0, 200) + '...');
            }
        }
        
        console.log('\n🎯 Next Steps:');
        console.log('1. Add the MCP server to Augment settings');
        console.log('2. Restart Augment');
        console.log('3. Try asking Augment to use research tools');
        
        process.exit(0);
    }, 3000);
    
} catch (error) {
    console.log('❌ Error testing server:', error.message);
    process.exit(1);
}
