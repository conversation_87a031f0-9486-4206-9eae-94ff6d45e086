#!/usr/bin/env node

// Test script to verify Vertex AI MCP Server setup
import { config } from 'dotenv';
import { existsSync } from 'fs';
import { join } from 'path';

// Load environment variables
config();

console.log('🧪 Testing Vertex AI MCP Server Setup...\n');

// Test 1: Check environment variables
console.log('1️⃣ Checking Environment Variables:');
const provider = process.env.AI_PROVIDER || 'vertex';
console.log(`   📋 AI Provider: ${provider}`);

let envVarsOk = true;
let requiredEnvVars = [];

if (provider === 'gemini') {
    requiredEnvVars = [
        'AI_PROVIDER',
        'GEMINI_API_KEY',
        'GEMINI_MODEL_ID'
    ];
} else {
    requiredEnvVars = [
        'GOOGLE_CLOUD_PROJECT',
        'GOOGLE_CLOUD_LOCATION',
        'GOOGLE_APPLICATION_CREDENTIALS',
        'VERTEX_AI_MODEL_ID'
    ];
}

for (const envVar of requiredEnvVars) {
    const value = process.env[envVar];
    if (value) {
        // Mask API keys for security
        const displayValue = envVar.includes('API_KEY') ?
            value.substring(0, 10) + '...' + value.substring(value.length - 4) :
            value;
        console.log(`   ✅ ${envVar}: ${displayValue}`);
    } else {
        console.log(`   ❌ ${envVar}: Not set`);
        envVarsOk = false;
    }
}

// Test 2: Check credentials (different for each provider)
console.log('\n2️⃣ Checking Authentication:');
if (provider === 'gemini') {
    const apiKey = process.env.GEMINI_API_KEY;
    if (apiKey && apiKey.startsWith('AIza')) {
        console.log(`   ✅ Gemini API key format is valid`);
    } else {
        console.log(`   ❌ Gemini API key is missing or invalid format`);
        envVarsOk = false;
    }
} else {
    const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
    if (credentialsPath && existsSync(credentialsPath)) {
        console.log(`   ✅ Credentials file exists: ${credentialsPath}`);
    } else {
        console.log(`   ❌ Credentials file not found: ${credentialsPath}`);
        envVarsOk = false;
    }
}

// Test 3: Check build files
console.log('\n3️⃣ Checking Build Files:');
const buildPath = join(process.cwd(), 'build', 'index.js');
if (existsSync(buildPath)) {
    console.log(`   ✅ Build file exists: ${buildPath}`);
} else {
    console.log(`   ❌ Build file not found: ${buildPath}`);
    envVarsOk = false;
}

// Test 4: Try to import the MCP server
console.log('\n4️⃣ Testing MCP Server Import:');
try {
    // This will test if the server can be imported without errors
    const serverPath = join(process.cwd(), 'build', 'index.js');
    console.log(`   ✅ MCP Server can be imported from: ${serverPath}`);
} catch (error) {
    console.log(`   ❌ Error importing MCP Server: ${error.message}`);
    envVarsOk = false;
}

// Summary
console.log('\n📋 Setup Summary:');
if (envVarsOk) {
    console.log('   ✅ All checks passed! Your Vertex AI MCP Server is ready to use.');
    console.log('\n🔧 Next Steps:');
    console.log('   1. Add this to your Augment MCP server configuration:');
    console.log(`      Command: ${buildPath}`);
    console.log('   2. Set the environment variables in Augment settings');
    console.log('   3. Restart Augment to load the new MCP server');
} else {
    console.log('   ❌ Some checks failed. Please review the errors above.');
    console.log('\n🔧 To fix issues:');
    console.log('   1. Run ./setup.sh to configure Google Cloud credentials');
    console.log('   2. Ensure all environment variables are set in .env file');
    console.log('   3. Run this test again: node test-setup.js');
}

console.log('\n📖 For detailed setup instructions, see: vertex-ai-mcp-server-setup-guide.md');
