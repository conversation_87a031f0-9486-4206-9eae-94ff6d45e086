#!/bin/bash

# Vertex AI MCP Server Setup Script
echo "🚀 Setting up Vertex AI MCP Server..."

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install it first:"
    echo "   https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Get project ID
echo "📋 Please enter your Google Cloud Project ID:"
read -r PROJECT_ID

if [ -z "$PROJECT_ID" ]; then
    echo "❌ Project ID cannot be empty"
    exit 1
fi

echo "🔧 Setting up Google Cloud project: $PROJECT_ID"

# Set the project
gcloud config set project "$PROJECT_ID"

# Enable required APIs
echo "🔌 Enabling required APIs..."
gcloud services enable aiplatform.googleapis.com
gcloud services enable cloudresourcemanager.googleapis.com

# Create service account
echo "👤 Creating service account..."
gcloud iam service-accounts create vertex-ai-mcp-server \
    --description="Service account for Vertex AI MCP Server" \
    --display-name="Vertex AI MCP Server" 2>/dev/null || echo "Service account already exists"

# Grant permissions
echo "🔐 Granting permissions..."
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:vertex-ai-mcp-server@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"

# Create service account key
echo "🔑 Creating service account key..."
KEY_PATH="$HOME/vertex-ai-mcp-server-key.json"
gcloud iam service-accounts keys create "$KEY_PATH" \
    --iam-account="vertex-ai-mcp-server@$PROJECT_ID.iam.gserviceaccount.com"

# Create .env file
echo "📝 Creating .env file..."
cat > .env << EOF
# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=$PROJECT_ID
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=$KEY_PATH

# Vertex AI Model Configuration
VERTEX_AI_MODEL_ID=gemini-2.5-pro-exp-03-25
VERTEX_AI_TEMPERATURE=0.1
VERTEX_AI_USE_STREAMING=true
EOF

echo "✅ Setup complete!"
echo ""
echo "📋 Configuration Summary:"
echo "   Project ID: $PROJECT_ID"
echo "   Service Account Key: $KEY_PATH"
echo "   Environment File: $(pwd)/.env"
echo ""
echo "🔧 Next Steps:"
echo "1. Add this MCP server to your Augment configuration:"
echo "   Command: $(pwd)/build/index.js"
echo "2. Set the environment variables in Augment settings"
echo "3. Test the connection with: node build/index.js"
echo ""
echo "📖 For detailed instructions, see: vertex-ai-mcp-server-setup-guide.md"
